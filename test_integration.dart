import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

void main() {
  runApp(const TestIntegrationApp());
}

class TestIntegrationApp extends StatelessWidget {
  const TestIntegrationApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Backend Integration Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const TestScreen(),
    );
  }
}

class TestScreen extends StatefulWidget {
  const TestScreen({Key? key}) : super(key: key);

  @override
  State<TestScreen> createState() => _TestScreenState();
}

class _TestScreenState extends State<TestScreen> {
  String _status = 'Not tested';
  String _quoteText = '';
  String _author = '';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Backend Integration Test'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Backend Status: $_status',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    if (_quoteText.isNotEmpty) ...[
                      const Text('Latest Quote:', style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      Text('"$_quoteText"', style: const TextStyle(fontStyle: FontStyle.italic)),
                      const SizedBox(height: 4),
                      Text('- $_author', style: const TextStyle(fontWeight: FontWeight.w500)),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isLoading ? null : _testBackendConnection,
              child: _isLoading 
                  ? const CircularProgressIndicator()
                  : const Text('Test Backend Connection'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isLoading ? null : _testRandomQuote,
              child: _isLoading 
                  ? const CircularProgressIndicator()
                  : const Text('Get Random Quote'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isLoading ? null : _testCategories,
              child: _isLoading 
                  ? const CircularProgressIndicator()
                  : const Text('Test Categories'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isLoading ? null : _testLogin,
              child: _isLoading 
                  ? const CircularProgressIndicator()
                  : const Text('Test Demo Login'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testBackendConnection() async {
    setState(() {
      _isLoading = true;
      _status = 'Testing...';
    });

    try {
      final response = await http.get(
        Uri.parse('http://localhost:8000/health'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          _status = 'Connected! Status: ${data['status']}, DB: ${data['database']}';
        });
      } else {
        setState(() {
          _status = 'Error: HTTP ${response.statusCode}';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'Connection failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testRandomQuote() async {
    setState(() {
      _isLoading = true;
      _status = 'Fetching quote...';
    });

    try {
      final response = await http.get(
        Uri.parse('http://localhost:8000/api/v1/quotes/random'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          _status = 'Quote fetched successfully!';
          _quoteText = data['text'] ?? '';
          _author = data['author'] ?? '';
        });
      } else {
        setState(() {
          _status = 'Quote fetch failed: HTTP ${response.statusCode}';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'Quote fetch error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testCategories() async {
    setState(() {
      _isLoading = true;
      _status = 'Fetching categories...';
    });

    try {
      final response = await http.get(
        Uri.parse('http://localhost:8000/api/v1/categories/'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as List;
        setState(() {
          _status = 'Categories fetched! Found ${data.length} categories';
        });
      } else {
        setState(() {
          _status = 'Categories fetch failed: HTTP ${response.statusCode}';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'Categories fetch error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testLogin() async {
    setState(() {
      _isLoading = true;
      _status = 'Testing login...';
    });

    try {
      final response = await http.post(
        Uri.parse('http://localhost:8000/api/v1/auth/login'),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: 'username=demo&password=demo123',
      ).timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          _status = 'Login successful! Token received: ${data['access_token']?.substring(0, 20)}...';
        });
      } else {
        setState(() {
          _status = 'Login failed: HTTP ${response.statusCode} - ${response.body}';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'Login error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
