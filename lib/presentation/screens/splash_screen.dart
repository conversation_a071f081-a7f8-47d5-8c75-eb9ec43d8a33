import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../../core/theme/app_theme.dart';
import '../providers/preferences_provider.dart'; 
import 'onboarding_screen.dart';
import 'home_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  late AnimationController _backgroundController;
  
  @override
  void initState() {
    super.initState();
    
    _logoController = AnimationController(
      duration: AppTheme.slowAnimation,
      vsync: this,
    );
    
    _textController = AnimationController(
      duration: AppTheme.normalAnimation,
      vsync: this,
    );
    
    _backgroundController = AnimationController(
      duration: AppTheme.extraSlowAnimation,
      vsync: this,
    );
    
    _startAnimations();
  }

  void _startAnimations() async {
    // Start background animation
    _backgroundController.forward();
    
    // Delay and start logo animation
    await Future.delayed(const Duration(milliseconds: 300));
    _logoController.forward();
    
    // Delay and start text animation
    await Future.delayed(const Duration(milliseconds: 500));
    _textController.forward();
    
    // Wait for animations to complete and navigate
    await Future.delayed(const Duration(milliseconds: 2000));
    _navigateToNextScreen();
  }

  void _navigateToNextScreen() {
    final preferencesProvider = Provider.of<PreferencesProvider>(context, listen: false);
    
    if (preferencesProvider.onboardingComplete) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => const HomeScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: AppTheme.normalAnimation,
        ),
      );
    } else {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => const OnboardingScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(1.0, 0.0),
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: AppTheme.defaultCurve,
              )),
              child: child,
            );
          },
          transitionDuration: AppTheme.normalAnimation,
        ),
      );
    }
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    _backgroundController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final customColors = theme.customColors;
    
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: customColors.primaryGradient,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              Expanded(
                flex: 3,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Animated logo
                      AnimatedBuilder(
                        animation: _logoController,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _logoController.value,
                            child: Transform.rotate(
                              angle: _logoController.value * 0.5,
                              child: Container(
                                width: 120,
                                height: 120,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.2),
                                      blurRadius: 20,
                                      offset: const Offset(0, 10),
                                    ),
                                  ],
                                ),
                                child: const Icon(
                                  Icons.format_quote,
                                  size: 60,
                                  color: Color(0xFF6750A4),
                                ),
                              ),
                            ),
                          );
                        },
                      )
                      .animate(delay: 300.ms)
                      .shimmer(duration: 1500.ms, color: Colors.white.withOpacity(0.5))
                      .then()
                      .shake(duration: 500.ms),
                      
                      const SizedBox(height: 32),
                      
                      // Animated title
                      AnimatedBuilder(
                        animation: _textController,
                        builder: (context, child) {
                          return Opacity(
                            opacity: _textController.value,
                            child: Transform.translate(
                              offset: Offset(0, 20 * (1 - _textController.value)),
                              child: Text(
                                'Daily Motivator',
                                style: theme.textTheme.headlineLarge?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 1.2,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          );
                        },
                      )
                      .animate(delay: 800.ms)
                      .fadeIn(duration: 600.ms)
                      .slideY(begin: 0.3, end: 0),
                      
                      const SizedBox(height: 16),
                      
                      // Animated subtitle
                      Text(
                        'Your daily dose of inspiration',
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: Colors.white.withOpacity(0.9),
                          fontWeight: FontWeight.w300,
                        ),
                        textAlign: TextAlign.center,
                      )
                      .animate(delay: 1200.ms)
                      .fadeIn(duration: 600.ms)
                      .slideY(begin: 0.3, end: 0),
                    ],
                  ),
                ),
              ),
              
              // Loading indicator
              Expanded(
                flex: 1,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Animated loading dots
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(3, (index) {
                        return Container(
                          margin: const EdgeInsets.symmetric(horizontal: 4),
                          child: Container(
                            width: 12,
                            height: 12,
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                          )
                          .animate(delay: (index * 200).ms)
                          .fadeIn(duration: 600.ms)
                          .then(delay: 200.ms)
                          .scaleXY(begin: 1.0, end: 1.5, duration: 400.ms)
                          .then()
                          .scaleXY(begin: 1.5, end: 1.0, duration: 400.ms),
                        );
                      }),
                    )
                    .animate(delay: 1500.ms)
                    .fadeIn(),
                    
                    const SizedBox(height: 24),
                    
                    // Loading text
                    Text(
                      'Loading your motivation...',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withOpacity(0.8),
                      ),
                    )
                    .animate(delay: 1800.ms)
                    .fadeIn(duration: 600.ms),
                  ],
                ),
              ),
              
              // Version info
              Padding(
                padding: const EdgeInsets.only(bottom: 32),
                child: Text(
                  'Version 2.0.0',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.white.withOpacity(0.6),
                  ),
                )
                .animate(delay: 2000.ms)
                .fadeIn(duration: 400.ms),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
