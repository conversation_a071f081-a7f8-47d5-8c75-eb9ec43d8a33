import 'package:flutter/material.dart';
import '../../domain/entities/quote.dart'; 
import 'quote_provider.dart';
import 'preferences_provider.dart';

/// Analytics provider that depends on both QuoteProvider and PreferencesProvider
/// This demonstrates ProxyProvider usage - it reacts to changes in other providers
class AnalyticsProvider extends ChangeNotifier {
  final QuoteProvider _quoteProvider;
  final PreferencesProvider _preferencesProvider;

  AnalyticsProvider({
    required QuoteProvider quoteProvider,
    required PreferencesProvider preferencesProvider,
  })  : _quoteProvider = quoteProvider,
        _preferencesProvider = preferencesProvider {
    // Listen to changes in dependencies
    _quoteProvider.addListener(_onQuoteProviderChanged);
    _preferencesProvider.addListener(_onPreferencesProviderChanged);
    _calculateAnalytics();
  }

  // Analytics data
  Map<String, int> _categoryStats = {};
  Map<String, int> _dailyQuoteCount = {};
  int _totalQuotesViewed = 0;
  int _currentStreak = 0;
  int _longestStreak = 0;
  String? _favoriteCategory;
  double _averageQuotesPerDay = 0.0;
  List<String> _recentCategories = [];
  Map<String, double> _categoryEngagement = {};
  DateTime? _lastAnalyticsUpdate;

  // Getters
  Map<String, int> get categoryStats => Map.unmodifiable(_categoryStats);
  Map<String, int> get dailyQuoteCount => Map.unmodifiable(_dailyQuoteCount);
  int get totalQuotesViewed => _totalQuotesViewed;
  int get currentStreak => _currentStreak;
  int get longestStreak => _longestStreak;
  String? get favoriteCategory => _favoriteCategory;
  double get averageQuotesPerDay => _averageQuotesPerDay;
  List<String> get recentCategories => List.unmodifiable(_recentCategories);
  Map<String, double> get categoryEngagement => Map.unmodifiable(_categoryEngagement);
  DateTime? get lastAnalyticsUpdate => _lastAnalyticsUpdate;

  // Computed analytics
  bool get hasViewedQuotesToday {
    final today = DateTime.now();
    final todayKey = '${today.year}-${today.month}-${today.day}';
    return _dailyQuoteCount.containsKey(todayKey) && _dailyQuoteCount[todayKey]! > 0;
  }

  int get quotesTodayCount {
    final today = DateTime.now();
    tices and shre final todayKey = '${today.year}-${today.month}-${today.day}';
    return _dailyQuoteCount[todayKey] ?? 0;
  }

  List<String> get topCategories {
    final sorted = _categoryStats.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    return sorted.take(5).map((e) => e.key).toList();
  }

  double get engagementScore {
    if (_totalQuotesViewed == 0) return 0.0;
    final daysActive = _dailyQuoteCount.length;
    if (daysActive == 0) return 0.0;
    return (_totalQuotesViewed / daysActive).clamp(0.0, 10.0);
  }

  // React to quote provider changes
  void _onQuoteProviderChanged() {
    final currentQuote = _quoteProvider.currentQuote;
    if (currentQuote != null && _quoteProvider.status == QuoteStatus.loaded) {
      _recordQuoteView(currentQuote);
    }
    _calculateAnalytics();
  }

  // React to preferences provider changes
  void _onPreferencesProviderChanged() {
    _calculateAnalytics();
  }

  // Record a quote view
  void _recordQuoteView(Quote quote) {
    // Update category stats
    _categoryStats[quote.category] = (_categoryStats[quote.category] ?? 0) + 1;

    // Update daily count
    final today = DateTime.now();
    final todayKey = '${today.year}-${today.month}-${today.day}';
    _dailyQuoteCount[todayKey] = (_dailyQuoteCount[todayKey] ?? 0) + 1;

    // Update total
    _totalQuotesViewed++;

    // Update recent categories
    _recentCategories.insert(0, quote.category);
    if (_recentCategories.length > 10) {
      _recentCategories = _recentCategories.take(10).toList();
    }

    // Calculate engagement for this category
    _updateCategoryEngagement(quote.category);

    _lastAnalyticsUpdate = DateTime.now();
    notifyListeners();
  }

  // Calculate comprehensive analytics
  void _calculateAnalytics() {
    _calculateFavoriteCategory();
    _calculateStreaks();
    _calculateAverageQuotesPerDay();
    _calculateCategoryEngagement();
    notifyListeners();
  }

  // Calculate favorite category based on user preferences and usage
  void _calculateFavoriteCategory() {
    if (_categoryStats.isEmpty) {
      _favoriteCategory = null;
      return;
    }

    // Weight categories by both usage and user preferences
    final selectedCategories = _preferencesProvider.selectedCategories;
    final weightedStats = <String, double>{};

    for (final entry in _categoryStats.entries) {
      double weight = entry.value.toDouble();
      
      // Boost weight if it's in user's selected categories
      if (selectedCategories.contains(entry.key)) {
        weight *= 1.5;
      }
      
      weightedStats[entry.key] = weight;
    }

    final sorted = weightedStats.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    _favoriteCategory = sorted.isNotEmpty ? sorted.first.key : null;
  }

  // Calculate current and longest streaks
  void _calculateStreaks() {
    if (_dailyQuoteCount.isEmpty) {
      _currentStreak = 0;
      _longestStreak = 0;
      return;
    }

    final sortedDates = _dailyQuoteCount.keys.toList()..sort();
    final today = DateTime.now();
    
    // Calculate current streak
    _currentStreak = 0;
    for (int i = 0; i < 365; i++) {
      final checkDate = today.subtract(Duration(days: i));
      final dateKey = '${checkDate.year}-${checkDate.month}-${checkDate.day}';
      
      if (_dailyQuoteCount.containsKey(dateKey) && _dailyQuoteCount[dateKey]! > 0) {
        _currentStreak++;
      } else {
        break;
      }
    }

    // Calculate longest streak
    _longestStreak = 0;
    int tempStreak = 0;
    DateTime? lastDate;

    for (final dateKey in sortedDates) {
      final parts = dateKey.split('-');
      final date = DateTime(int.parse(parts[0]), int.parse(parts[1]), int.parse(parts[2]));
      
      if (lastDate == null || date.difference(lastDate).inDays == 1) {
        tempStreak++;
        _longestStreak = _longestStreak > tempStreak ? _longestStreak : tempStreak;
      } else if (date.difference(lastDate).inDays > 1) {
        tempStreak = 1;
      }
      
      lastDate = date;
    }
  }

  // Calculate average quotes per day
  void _calculateAverageQuotesPerDay() {
    if (_dailyQuoteCount.isEmpty) {
      _averageQuotesPerDay = 0.0;
      return;
    }

    final totalDays = _dailyQuoteCount.length;
    _averageQuotesPerDay = _totalQuotesViewed / totalDays;
  }

  // Update category engagement scores
  void _updateCategoryEngagement(String category) {
    final selectedCategories = _preferencesProvider.selectedCategories;
    final isSelected = selectedCategories.contains(category);
    
    // Base engagement from view count
    final viewCount = _categoryStats[category] ?? 0;
    double engagement = viewCount.toDouble();
    
    // Boost if it's a selected category
    if (isSelected) {
      engagement *= 1.2;
    }
    
    // Normalize to 0-100 scale
    final maxViews = _categoryStats.values.isNotEmpty 
        ? _categoryStats.values.reduce((a, b) => a > b ? a : b) 
        : 1;
    
    _categoryEngagement[category] = (engagement / maxViews * 100).clamp(0.0, 100.0);
  }

  // Calculate engagement for all categories
  void _calculateCategoryEngagement() {
    for (final category in _categoryStats.keys) {
      _updateCategoryEngagement(category);
    }
  }

  // Get insights based on analytics
  List<String> getInsights() {
    final insights = <String>[];

    if (_currentStreak > 0) {
      insights.add('🔥 You\'re on a ${_currentStreak}-day streak!');
    }

    if (_favoriteCategory != null) {
      insights.add('💡 Your favorite category is $_favoriteCategory');
    }

    if (_averageQuotesPerDay > 3) {
      insights.add('📚 You\'re reading ${_averageQuotesPerDay.toStringAsFixed(1)} quotes per day on average');
    }

    if (quotesTodayCount == 0) {
      insights.add('🌟 Start your day with some motivation!');
    }

    if (_longestStreak > 7) {
      insights.add('🏆 Your longest streak was $_longestStreak days!');
    }

    return insights;
  }

  // Reset analytics
  void resetAnalytics() {
    _categoryStats.clear();
    _dailyQuoteCount.clear();
    _totalQuotesViewed = 0;
    _currentStreak = 0;
    _longestStreak = 0;
    _favoriteCategory = null;
    _averageQuotesPerDay = 0.0;
    _recentCategories.clear();
    _categoryEngagement.clear();
    _lastAnalyticsUpdate = null;
    notifyListeners();
  }

  @override
  void dispose() {
    _quoteProvider.removeListener(_onQuoteProviderChanged);
    _preferencesProvider.removeListener(_onPreferencesProviderChanged);
    super.dispose();
  }
}
