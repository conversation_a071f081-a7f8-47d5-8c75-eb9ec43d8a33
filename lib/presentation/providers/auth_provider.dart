import 'package:flutter/foundation.dart';
import '../../data/datasources/auth_datasource.dart';
import '../../core/services/auth_service.dart';
import '../../core/errors/exceptions.dart';

enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

class AuthProvider extends ChangeNotifier {
  final AuthDataSource authDataSource;
  final AuthService authService;

  AuthProvider({
    required this.authDataSource,
    required this.authService,
  });

  AuthState _state = AuthState.initial;
  String? _errorMessage;
  Map<String, dynamic>? _currentUser;

  // Getters
  AuthState get state => _state;
  String? get errorMessage => _errorMessage;
  Map<String, dynamic>? get currentUser => _currentUser;
  bool get isAuthenticated => _state == AuthState.authenticated;
  bool get isLoading => _state == AuthState.loading;

  // Initialize auth state
  Future<void> initialize() async {
    _setState(AuthState.loading);
    
    try {
      if (authService.isAuthenticated && !authService.isTokenExpired()) {
        // Try to get current user info
        final user = await authDataSource.getCurrentUser();
        _currentUser = user;
        _setState(AuthState.authenticated);
      } else {
        _setState(AuthState.unauthenticated);
      }
    } catch (e) {
      // If token is invalid, clear auth and set unauthenticated
      await authService.clearAuth();
      _setState(AuthState.unauthenticated);
    }
  }

  // Login
  Future<bool> login(String username, String password) async {
    _setState(AuthState.loading);
    _clearError();

    try {
      final response = await authDataSource.login(username, password);
      _currentUser = response['user'] as Map<String, dynamic>?;
      _setState(AuthState.authenticated);
      return true;
    } catch (e) {
      _setError(_getErrorMessage(e));
      _setState(AuthState.unauthenticated);
      return false;
    }
  }

  // Register
  Future<bool> register({
    required String email,
    required String username,
    required String password,
    required String fullName,
  }) async {
    _setState(AuthState.loading);
    _clearError();

    try {
      final response = await authDataSource.register(
        email: email,
        username: username,
        password: password,
        fullName: fullName,
      );
      
      // If registration includes auto-login
      if (response['access_token'] != null) {
        _currentUser = response['user'] as Map<String, dynamic>?;
        _setState(AuthState.authenticated);
      } else {
        _setState(AuthState.unauthenticated);
      }
      
      return true;
    } catch (e) {
      _setError(_getErrorMessage(e));
      _setState(AuthState.unauthenticated);
      return false;
    }
  }

  // Logout
  Future<void> logout() async {
    _setState(AuthState.loading);
    
    try {
      await authDataSource.logout();
    } catch (e) {
      // Continue with logout even if server call fails
      print('Logout error: $e');
    } finally {
      _currentUser = null;
      _setState(AuthState.unauthenticated);
    }
  }

  // Refresh token
  Future<bool> refreshToken() async {
    try {
      await authDataSource.refreshToken();
      return true;
    } catch (e) {
      // If refresh fails, logout user
      await logout();
      return false;
    }
  }

  // Update user data
  Future<void> updateUserData() async {
    if (!isAuthenticated) return;

    try {
      final user = await authDataSource.getCurrentUser();
      _currentUser = user;
      notifyListeners();
    } catch (e) {
      print('Failed to update user data: $e');
    }
  }

  // Helper methods
  void _setState(AuthState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  String _getErrorMessage(dynamic error) {
    if (error is ServerException) {
      return error.message;
    } else if (error is NetworkException) {
      return error.message;
    } else {
      return 'An unexpected error occurred';
    }
  }

  // User info getters
  String? get userId => _currentUser?['id']?.toString();
  String? get userEmail => _currentUser?['email']?.toString();
  String? get userName => _currentUser?['full_name']?.toString() ?? _currentUser?['username']?.toString();
  String? get userUsername => _currentUser?['username']?.toString();
  bool get isAdmin => _currentUser?['is_superuser'] == true;
  int get userPoints => _currentUser?['points'] as int? ?? 0;
  int get userLevel => _currentUser?['level'] as int? ?? 1;
  int get userStreak => _currentUser?['current_streak'] as int? ?? 0;
}
